#!/usr/bin/env python3
"""
提取对话内容的脚本
- 对于"role": "user"的消息，提取"content"字段
- 对于"role": "assistant"的消息，"content"字段永远只使用原文件"content"字段；如果有"thinking"字段，只有"thinking"字段需要处理"raw_content"字段
"""

import json
import re
from pathlib import Path
from typing import List, Dict, Any


def render_html(text: str) -> str:
    """
    处理HTML标签，移除thinking部分，保留实际回复内容
    """
    # 1. 移除 <summary> 标签及其内容
    text = re.sub(r'<summary.*?>.*?</summary>', '', text, flags=re.DOTALL)

    # 2. 替换 <br> 标签为换行符
    text = text.replace('<br>', '\n').replace('<br/>', '\n')

    # 3. 移除其他 HTML 标签（保留文本内容）
    text = re.sub(r'<[^>]+>', '', text)

    # 4. 清理多余空白
    text = re.sub(r'\n\s*\n', '\n', text)  # 移除多余空行
    text = re.sub(r'[ \t]+', ' ', text)    # 合并多余空格

    return text.strip()


def remove_parentheses_and_content(text: str) -> str:
    """
    移除括号及括号内的内容（中英文括号）
    """
    # 正则表达式匹配中英文括号及括号内的内容
    pattern = r'[\(（].*?[\)）]'
    # 使用 re.sub 替换匹配的内容为空字符串
    result = re.sub(pattern, '', text)
    if result.strip():
        return result.strip()
    else:
        return text  # 如果移除后为空，返回原文本


def extract_assistant_content_and_thinking(msg: Dict[str, Any]) -> tuple[str, str]:
    """
    从assistant消息中提取内容和思考过程
    返回 (content, thinking) 元组
    """
    # content字段永远只使用原文件的"content"字段
    content = msg.get('content', '')
    thinking = ""

    # 只有当需要thinking时，才从raw_content中提取thinking部分
    if 'raw_content' in msg and msg['raw_content']:
        raw_content = msg['raw_content']

        # 提取thinking部分（</details>标签之前的内容）
        details_end_tag = "</details>"
        details_end_pos = raw_content.find(details_end_tag)

        if details_end_pos != -1:
            thinking_raw = raw_content[:details_end_pos + len(details_end_tag)].strip()
            thinking = render_html(thinking_raw)

    return content, thinking


def process_dialogue(raw_dialogue: List[Dict[str, Any]],
                    remove_brackets_in_user: bool = True,
                    include_thinking: bool = False) -> List[Dict[str, str]]:
    """
    处理单个对话，提取用户和助手的内容
    """
    if not raw_dialogue:
        return []

    processed_dialogue = []

    for msg in raw_dialogue:
        role = msg.get('role', '')

        if role == 'user':
            # 提取用户内容
            content = msg.get('content', '')
            if remove_brackets_in_user:
                content = remove_parentheses_and_content(content)

            processed_dialogue.append({
                'role': 'user',
                'content': content
            })

        elif role == 'assistant':
            # 提取助手内容和思考过程
            content, thinking = extract_assistant_content_and_thinking(msg)

            if include_thinking:
                # 包含思考过程的模式
                processed_msg = {
                    'role': 'assistant',
                    'content': content
                }
                if thinking:
                    processed_msg['thinking'] = thinking
                processed_dialogue.append(processed_msg)
            else:
                # 只包含对话内容的模式
                processed_dialogue.append({
                    'role': 'assistant',
                    'content': content
                })

    return processed_dialogue


def extract_persona_id(dialogue_group: List[Dict[str, Any]]) -> str:
    """
    从对话组中提取persona_id
    """
    for msg in dialogue_group:
        if isinstance(msg, dict) and 'settings' in msg:
            settings = msg['settings']
            if isinstance(settings, dict) and 'persona_id' in settings:
                return settings['persona_id']
    return ""


def process_dialogues_file(input_file: str,
                          output_file: str,
                          remove_brackets_in_user: bool = True,
                          include_thinking: bool = False) -> None:
    """
    处理整个对话文件
    """
    mode_desc = "包含思考过程" if include_thinking else "仅对话内容"
    print(f"开始处理文件: {input_file} (模式: {mode_desc})")

    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)

        print(f"文件加载成功，开始处理对话...")

        processed_data = []
        total_dialogues = 0

        # 处理每个对话组
        for dialogue_group in raw_data:
            if isinstance(dialogue_group, list):
                # 提取persona_id
                persona_id = extract_persona_id(dialogue_group)

                # 处理对话内容
                processed_dialogue = process_dialogue(
                    dialogue_group,
                    remove_brackets_in_user,
                    include_thinking
                )

                if processed_dialogue:  # 只添加非空对话
                    # 创建包含persona_id的对话对象
                    dialogue_obj = {
                        'persona_id': persona_id,
                        'messages': processed_dialogue
                    }
                    processed_data.append(dialogue_obj)
                    total_dialogues += 1

        # 保存处理后的数据
        Path(output_file).parent.mkdir(parents=True, exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(processed_data, f, ensure_ascii=False, indent=2)

        print(f"处理完成！")
        print(f"- 总对话数: {total_dialogues}")
        print(f"- 输出文件: {output_file}")

    except Exception as e:
        print(f"处理文件时出错: {e}")


def main():
    """
    主函数 - 生成两种格式的输出文件
    """
    input_file = "merged_batch_000-500.json"

    print("对话内容提取工具")
    print("=" * 50)

    # 模式1: 仅对话内容（类似OpenAI格式）
    print("\n处理模式1: 仅对话内容")
    process_dialogues_file(
        input_file=input_file,
        output_file="processed_dialogues_openai.json",
        remove_brackets_in_user=True,
        include_thinking=False
    )

    # 模式2: 包含思考过程
    print("\n处理模式2: 包含思考过程")
    process_dialogues_file(
        input_file=input_file,
        output_file="processed_dialogues_thinking.json",
        remove_brackets_in_user=True,
        include_thinking=True
    )

    print("\n全部处理完成！")
    print("生成文件:")
    print("- processed_dialogues_openai.json (仅对话内容)")
    print("- processed_dialogues_thinking.json (包含思考过程)")


if __name__ == "__main__":
    main()