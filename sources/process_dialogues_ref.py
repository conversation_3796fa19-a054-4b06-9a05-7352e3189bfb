import json
import re
from pathlib import Path
from typing import List


def render_html(text):
    # 1. 移除 <summary> 标签及其内容
    text = re.sub(r'<summary.*?>.*?</summary>', '', text, flags=re.DOTALL)
    
    # 2. 替换 <br> 标签为换行符（根据需求可调整）
    text = text.replace('<br>', '\n').replace('<br/>', '\n')
    
    # 3. 移除其他 HTML 标签（保留文本内容）
    text = re.sub(r'<[^>]+>', '', text)
    
    # 4. 清理多余空白（可选）
    # text = re.sub(r'\s+', ' ', text).strip()
    
    return text.strip()


def remove_parentheses_and_content(text):
    # 正则表达式匹配中英文括号及括号内的内容
    pattern = r'[\(（].*?[\)）]'
    # 使用 re.sub 替换匹配的内容为空字符串
    result = re.sub(pattern, '', text)
    if result.strip():
        return result.strip()
    else:
        return result


def convert_to_openai(raw_dialogue: List[dict], remove_brackets_in_user_contents: bool = True):
    """
    忽略所有中间结果（思考过程、中间模块等），将对话处理成标准openai格式
    """
    if not raw_dialogue:
        return raw_dialogue
    
    dialogue = []
    for msg in raw_dialogue:
        new_msg = {"role": msg['role'], "content": msg['content']}
        if new_msg['role'] == 'user' and remove_brackets_in_user_contents:
            new_msg['content'] = remove_parentheses_and_content(new_msg['content'])
        dialogue.append(new_msg)
    return dialogue


def convert_to_thinking(raw_dialogue: List[dict], remove_brackets_in_user_contents: bool = True, split: bool = False):
    """
    将对话处理成思考形式
    """
    if not raw_dialogue:
        return raw_dialogue
    
    new_messages = []
    for msg in raw_dialogue:
        if msg['role'] == 'user' and remove_brackets_in_user_contents:
            msg['content'] = remove_parentheses_and_content(msg['content'])
        
        if msg['role'] == "assistant":
            tag = "</details>"
            tag_pos = msg['raw_content'].find(tag)
            if tag_pos != -1:
                thinking = msg['raw_content'][: tag_pos + len(tag)].strip()
                thinking = render_html(thinking)
            else:
                thinking = ""
            
            msg['thinking'] = thinking
            
        new_msg = {}
        for key in ("role", "content", "thinking"):
            if key in msg:
                new_msg[key] = msg[key]
        new_messages.append(new_msg)
    
        
    if not split:
        return new_messages
    else:
        all_thinking_dialogues = []
        for i in range(len(new_messages)):
            if i > 0 and new_messages[i]['role'] == 'assistant':
                all_thinking_dialogues.append(new_messages[:i+1])
        return all_thinking_dialogues


def main(*,
         input_file: str,
         output_file: str,
         ignore_interal: bool = True,
         remove_brackets_in_user_contents: bool = True,
         split: bool = False):
    with open(input_file, 'r', encoding='utf-8') as reader:
        raw_data = json.load(reader)
    
    data = []
    for raw_dialogue in raw_data:
        if ignore_interal:
            dialogue = convert_to_openai(raw_dialogue, remove_brackets_in_user_contents=remove_brackets_in_user_contents)
            data.append(dialogue)
        else:
            result = convert_to_thinking(raw_dialogue, remove_brackets_in_user_contents=remove_brackets_in_user_contents, split=split)
            if split:
                data.extend(result)
            else:
                data.append(result)
    
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as writer:
        json.dump(data, writer, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    from fire import Fire
    Fire(main)
    # for file in (Path(__file__).parent / 'output_0716').glob("*.json"):
    #     main(
    #         input_file=file,
    #         output_file=Path(__file__).parent / 'output_0716_openai' / file.name,
    #         ignore_interal=True
    #     )
        
    #     main(
    #         input_file=file,
    #         output_file=Path(__file__).parent / 'output_0716_thinking' / file.name,
    #         ignore_interal=False
    #     )
